import React, { useState } from "react";
import { Formik, Form, ErrorMessage, Field } from "formik";
import * as Yup from "yup";
import { contactUs } from "../../../../api";
import { FORM_TYPES } from "../../../../lambda/constants";
import TextError from "../TextError";
import toast from "../Toast";
import { sliceIntoChunks } from "../../../../utils/helpers";

const SupplierDiversityForm = ({
  onSubmit,
  onSuccess,
  onError,
  showSubmitButton = true,
  submitButtonText = "Submit",
  submitButtonClass = "btn-primary",
  formClass = "",
  isModal = false,
  showInfoSection = false,
}) => {
  const [buttonSubmited, setButtonSubmited] = useState(false);

  const businessIntrestsOptions = [
    {
      key: "bi/architectureDesignEngineering",
      value: "Design",
    },
    { key: "bi/construction", value: "Construction" },
    { key: "bi/concessions", value: "Concessions" },
    { key: "bi/services", value: "Services" },
    { key: "bi/other", value: "Others" },
  ];

  const markets = [
    { key: "mi/chicago", value: "Chicago (ORD)" },
    { key: "mi/losAngles", value: "Los Angeles (LAX)" },
    { key: "mi/newYork", value: "New York (JFK T8 & New Terminal One)" },
  ];

  const companyDescriptionOptions = [
    { key: "cd/womanOwned", value: "Woman-owned" },
    { key: "cd/minorityOwned", value: "Minority-owned" },
    { key: "cd/veteranOwned", value: "Veteran-owned" },
    { key: "cd/acdbe", value: "ACDBE" },
    { key: "cd/other", value: "Other" },
  ];

  const initialValues = {
    firstName: "",
    lastName: "",
    mobile: "",
    email: "",
    company: "",
    website: "",
    businessInterests: [],
    marketInterests: [],
    companyDescriptions: [],
    otherInformation: "",
    registerNowEmailUpdates: false,
  };

  const validationSchema = Yup.object({
    firstName: Yup.string().required("First Name is required"),
    lastName: Yup.string().required("Last Name is required"),
    mobile: Yup.string()
      .required("Mobile Number is required")
      .matches(/^\d{10,14}$/, "Please enter a number with minimum 10 and maximum 14 digits"),
    email: Yup.string().email("Please enter a valid email").required("Email is required"),
    company: Yup.string().required("Company is required"),
    website: Yup.string()
      .matches(/((https?):\/\/)?(www.)?[a-z0-9]+(\.[a-z]{2,}){1,3}(#?\/?[a-zA-Z0-9#]+)*\/?(\?[a-zA-Z0-9-_]+=[a-zA-Z0-9-%]+&?)?$/, "Please enter website url in format 'www.yourwebsite.com'"),
    businessInterests: Yup.array().min(1, "Please select atlease one option").required(),
    marketInterests: Yup.array().min(1, "Please select atlease one option").required(),
    companyDescriptions: Yup.array().min(1, "Please select atlease one option").required(),
  });

  const handleBlur = (e) => {
    if (e.target.value == "") {
      e.target.classList.remove("has-value");
    } else {
      e.target.classList.add("has-value");
    }
  };

  const handleSubmit = async (values, { resetForm }) => {
    try {
      setButtonSubmited(true);

      // Transform the form data
      const transformedValues = { ...values };

      businessIntrestsOptions.map((item) => {
        transformedValues[item.key] = transformedValues.businessInterests.includes(item.value) ? true : false;
      });
      delete transformedValues.businessInterests;

      markets.map((item) => {
        transformedValues[item.key] = transformedValues.marketInterests.includes(item.value) ? true : false;
      });
      delete transformedValues.marketInterests;

      companyDescriptionOptions.map((item) => {
        transformedValues[item.key] = transformedValues.companyDescriptions.includes(item.value) ? true : false;
      });
      delete transformedValues.companyDescriptions;

      // Call custom onSubmit if provided, otherwise use default API call
      if (onSubmit) {
        await onSubmit(transformedValues, { resetForm });
      } else {
        await contactUs(FORM_TYPES.REGISTER_NOW, transformedValues);
        resetForm();
        toast({ type: "success", message: "Thank you!" });
      }

      setButtonSubmited(false);

      // Call success callback if provided
      if (onSuccess) {
        onSuccess(transformedValues);
      }
    } catch (error) {
      setButtonSubmited(false);

      // Call error callback if provided, otherwise show default error
      if (onError) {
        onError(error);
      } else {
        toast({ type: "error", message: "Something went wrong" });
      }
    }
  };

  const chunkedBusinessInterestOptions = sliceIntoChunks(businessIntrestsOptions, 3);
  const chunkedMarkets = sliceIntoChunks(markets, 1);
  const chunkedCompanyDescriptionOptions = sliceIntoChunks(companyDescriptionOptions, 3);

  return (
    <Formik
      enableReinitialize={true}
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
    >
      {({ handleChange, values, isSubmitting }) => {
        return (
          <div className={`supplier-diversity-form ${formClass}`}>
            {showInfoSection && (
              <div className="form-info-section">
                <div className="register-img">
                  <img src="/assets/images/register-img.jpg" alt="" />
                </div>
                <div className="register-info">
                  <h2>Join Our Supplier Diversity Network</h2>
                  <p>
                    Are you a minority-, female-owned, disadvantaged, or certified Airport Concessions Disadvantaged Business Enterprise (ACDBE) business interested in new business opportunities at
                    airports across the country?
                  </p>
                  <ul className="list">
                    <li>Connect with networking opportunities</li>
                    <li>Exclusive invitations to participate in new business opportunities in leasing, contracting, and more</li>
                    <li>Unlock access to mentorship and growth opportunities</li>
                  </ul>
                </div>
              </div>
            )}

            <Form>
              <div className="form">
                <div className="row">
                  <div className="col-6">
                    <div className="form-group">
                      <Field id="firstName" type="text" value={values.firstName} onChange={handleChange} className="form-control" onBlur={handleBlur} />
                      <label htmlFor="firstName">
                        First Name <span>*</span>
                      </label>
                    </div>
                    <ErrorMessage name="firstName" component={TextError} />
                  </div>
                  <div className="col-6">
                    <div className="form-group">
                      <Field id="lastName" type="text" value={values.lastName} onChange={handleChange} className="form-control" onBlur={handleBlur} />
                      <label htmlFor="lastName">
                        Last Name <span>*</span>
                      </label>
                    </div>
                    <ErrorMessage name="lastName" component={TextError} />
                  </div>
                </div>

                <div className="row">
                  <div className="col-6">
                    <div className="form-group">
                      <Field id="mobile" type="text" value={values.mobile} onChange={handleChange} className="form-control" onBlur={handleBlur} />
                      <label htmlFor="mobile">Phone Number <span>*</span></label>
                    </div>
                    <ErrorMessage name="mobile" component={TextError} />
                  </div>
                  <div className="col-6">
                    <div className="form-group">
                      <Field id="email" type="text" value={values.email} onChange={handleChange} className="form-control" onBlur={handleBlur} />
                      <label htmlFor="email">
                        Email <span>*</span>
                      </label>
                    </div>
                    <ErrorMessage name="email" component={TextError} />
                  </div>
                </div>

                <div className="row">
                  <div className="col-6">
                    <div className="form-group">
                      <Field id="company" type="text" value={values.company} onChange={handleChange} className="form-control" onBlur={handleBlur} />
                      <label htmlFor="company">
                        Company <span>*</span>
                      </label>
                    </div>
                    <ErrorMessage name="company" component={TextError} />
                  </div>
                  <div className="col-6">
                    <div className="form-group">
                      <Field id="website" type="text" value={values.website} onChange={handleChange} className="form-control" onBlur={handleBlur} />
                      <label htmlFor="website">
                        Website
                      </label>
                    </div>
                    <ErrorMessage name="website" component={TextError} />
                  </div>
                </div>

                <div className="checkbox-items">
                  <h4>What best describes your company? Select all that apply:</h4>
                  <div className="flex-space-between">
                    <Field name="companyDescriptions">
                      {({ field }) => {
                        return chunkedCompanyDescriptionOptions.map((option, index) => {
                          return (
                            <React.Fragment key={index}>
                              <div className="flex-col">
                                {option.map((item) => {
                                  return (
                                    <React.Fragment key={item.key}>
                                      <div className="checkbox">
                                        <input type="checkbox" id={item.key} {...field} value={item.value} />
                                        <label htmlFor={item.key}>{item.value}</label>
                                      </div>
                                    </React.Fragment>
                                  );
                                })}
                              </div>
                            </React.Fragment>
                          );
                        });
                      }}
                    </Field>
                  </div>
                </div>
                <div className="mg-tp-30">
                  <ErrorMessage name="companyDescriptions" component={TextError} />
                </div>

                <div className="checkbox-items">
                  <h4>I am interested in business opportunities in (select all that apply):</h4>
                  <div className="flex-space-between">
                    <Field name="businessInterests">
                      {({ field }) => {
                        return chunkedBusinessInterestOptions.map((option, index) => {
                          return (
                            <React.Fragment key={index}>
                              <div className="flex-col">
                                {option.map((item) => {
                                  return (
                                    <React.Fragment key={item.key}>
                                      <div className="checkbox">
                                        <input type="checkbox" id={item.key} {...field} value={item.value} />
                                        <label htmlFor={item.key}>{item.value}</label>
                                      </div>
                                    </React.Fragment>
                                  );
                                })}
                              </div>
                            </React.Fragment>
                          );
                        });
                      }}
                    </Field>
                  </div>
                </div>
                <div className="mg-tp-30">
                  <ErrorMessage name="businessInterests" component={TextError} />
                </div>

                <div className="checkbox-cities">
                  <h4>Is there a specific market you're interested in? Select all that apply:</h4>
                  <div className="flex-space-between">
                    <Field name="marketInterests">
                      {({ field }) => {
                        return chunkedMarkets.map((option, index) => {
                          return (
                            <React.Fragment key={index}>
                              <div className="flex-col">
                                {option.map((item) => {
                                  return (
                                    <React.Fragment key={item.key}>
                                      <div className="checkbox">
                                        <input type="checkbox" id={item.key} {...field} value={item.value} />
                                        <label htmlFor={item.key}>{item.value}</label>
                                      </div>
                                    </React.Fragment>
                                  );
                                })}
                              </div>
                            </React.Fragment>
                          );
                        });
                      }}
                    </Field>
                  </div>
                </div>
                <div className="mg-tp-30">
                  <ErrorMessage name="marketInterests" component={TextError} />
                </div>

                <div className="form-group">
                  <Field id="otherInformation" as="textarea" value={values.otherInformation} onChange={handleChange} className="form-control" onBlur={handleBlur} />
                  <label htmlFor="otherInformation">Anything else you'd like us to know?</label>
                </div>

                {showSubmitButton && (
                  <div className="form-group-footer">
                    <div className="form-group-footer-left">
                      <div className="checkbox">
                        <Field type="checkbox" id="registerNowEmailUpdates" name="registerNowEmailUpdates" />
                        <label htmlFor="registerNowEmailUpdates">
                          I'd like to receive occasional email updates from URW Airports, including information about Advance Network news and opportunities.
                        </label>
                      </div>
                      <p>
                        By submitting I agree to all
                        <a href="https://www.westfield.com/terms-and-conditions" target="_blank" className="link">
                          <strong>Terms of Use</strong>
                        </a>
                        and
                        <a href="https://www.urw.com/privacy-policy" target="_blank" className="link">
                          <strong>Privacy Policy</strong>
                        </a>
                      </p>
                    </div>
                    <div className="form-submit">
                      <input type="submit" className={submitButtonClass} value={submitButtonText} />
                      {buttonSubmited && <span>
                        <span className="loader"></span>
                      </span>}
                    </div>
                  </div>
                )}
              </div>
            </Form>
          </div>
        );
      }}
    </Formik>
  );
};

export default SupplierDiversityForm;

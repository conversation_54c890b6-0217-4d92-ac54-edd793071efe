## Include terragrunt config files
include "root" {
  path = find_in_parent_folders()
}

## Local values
locals {
  envvars              = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  globalvars           = read_terragrunt_config(find_in_parent_folders("global.hcl"))
  resource_prefix      = local.envvars.locals.resource_prefix
  env                  = local.envvars.locals.env
  project              = local.globalvars.locals.project
  aws_provider_version = local.globalvars.locals.aws_provider_version
  ses_domain_identity  = "e.urwairports.com"
  source_email         = "no-reply@${local.ses_domain_identity}"
  csv_exporter_bucket  = "${local.project}-${local.env}-contactus-responses"
}

## Dependencies
dependency "lambda_layer_nodejs" {
  config_path                             = "../lambda-layer-nodejs"
  mock_outputs_allowed_terraform_commands = get_terraform_commands_that_need_input()
  mock_outputs = {
    arn = "arn"
  }
  mock_outputs_merge_strategy_with_state = "shallow"
}

dependency "s3_contactus_responses" {
  config_path                             = "../s3-contactus-responses"
  mock_outputs_allowed_terraform_commands = get_terraform_commands_that_need_input()
  mock_outputs = {
    id  = "id"
    arn = "arn"
  }
  mock_outputs_merge_strategy_with_state = "shallow"
}

dependency "dyanmodb_contact_response" {
  config_path                             = "../dynamodb-contact-response"
  mock_outputs_allowed_terraform_commands = get_terraform_commands_that_need_input()
  mock_outputs = {
    name = "name"
  }
  mock_outputs_merge_strategy_with_state = "shallow"
}

dependency "dyanmodb_opportunity_response" {
  config_path                             = "../dynamodb-opportunity-response"
  mock_outputs_allowed_terraform_commands = get_terraform_commands_that_need_input()
  mock_outputs = {
    name = "name"
  }
  mock_outputs_merge_strategy_with_state = "shallow"
}

dependency "iam_user_s3_presigned_url_sigv4" {
  config_path                             = "../iam-user-s3-presigned-url-sigv4"
  mock_outputs_allowed_terraform_commands = get_terraform_commands_that_need_input()
  mock_outputs = {
    access_key_id     = "access_key_id",
    secret_access_key = "secret_access_key"
  }
  mock_outputs_merge_strategy_with_state = "shallow"
}

## Terraform config
terraform {
  source = "../../../..//modules/lambda"
}

## Provider versions config
generate "versions" {
  path      = "versions.tf"
  if_exists = "overwrite"
  contents  = <<EOF
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> ${local.aws_provider_version}"
    }
  }
}
EOF
}

inputs = {
  function_name           = "${local.resource_prefix}-csv-exporter"
  description             = "CSV exporter"
  handler                 = "lambda/csv-exporter/index.handler"
  lambda_runtime          = "nodejs22.x"
  source_code             = get_env("TF_lambda_source_code_file")
  lambda_artifacts_bucket = get_env("TF_lambda_artifacts_bucket")
  layers_arn              = [dependency.lambda_layer_nodejs.outputs.arn]
  lambda_timeout          = 300
  environment_variables = {
    PROPERTY_ID                              = "URW-AIRPORT"
    CSV_FILE_PATH                            = "/tmp"
    S3_BUCKET_NAME                           = local.csv_exporter_bucket
    PRESIGNED_URL_EXPIRATION                 = 259200
    OPPORTUNITY_RESPONSE_DYNAMODB_TABLE_NAME = dependency.dyanmodb_opportunity_response.outputs.name
    CONTACT_RESPONSE_DYNAMODB_TABLE_NAME     = dependency.dyanmodb_contact_response.outputs.name
    SOURCE_EMAIL                             = local.source_email
    HOLT_EMAIL                               = "<EMAIL>"
    MARKETING_EMAIL                          = "<EMAIL>"
    LEASING_EMAIL                            = "<EMAIL>"
    OPS_EMAIL                                = "<EMAIL>"
    CP_EMAIL                                 = "<EMAIL>"
    STAY_UPTO_DATE_MARKETING_EMAIL           = "<EMAIL>"
    CC_EMAILS                                = "<EMAIL>"
    NTO_EMAILS                               = "<EMAIL>"


    ## IAM user keys required to for sigv4 presigned url creation
    ## https://repost.aws/knowledge-center/presigned-url-s3-bucket-expiration
    S3_PRESIGNED_URL_ACCESS_KEY_ID     = dependency.iam_user_s3_presigned_url_sigv4.outputs.access_key_id
    S3_PRESIGNED_URL_SECRET_ACCESS_KEY = dependency.iam_user_s3_presigned_url_sigv4.outputs.secret_access_key
  }
  csv_exporter_bucket_arn             = dependency.s3_contactus_responses.outputs.arn
  opportunity_response_dyanmodb_table = dependency.dyanmodb_opportunity_response.outputs.name
  contact_response_dyanmodb_table     = dependency.dyanmodb_contact_response.outputs.name
  ses_domain_identity                 = local.ses_domain_identity
  source_email                        = local.source_email
}

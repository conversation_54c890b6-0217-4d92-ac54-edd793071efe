const DYNAMO_TABLES = {
  CONTACT_RESPONSE: process.env.CONTACT_RESPONSE_DYNAMODB_TABLE_NAME,
  OPPORTUNITY_RESPONSE: process.env.OPPORTUNITY_RESPONSE_DYNAMODB_TABLE_NAME,
};

const FORM_TYPES = {
  STAY_UP_TO_DATE: "stayUpToDate",
  CONTACT_US: "getInTouch",
  JFK_CONTACT_US: "jfkGetInTouch",
  REGISTER_NOW: "registerNow",
  BUSINESS_OPPORTUNITY: "businessOpportunity",
  EMPLOYMENT_OPPORTUNITY: "employmentOpportunity",
};

const REGISTRATION_OPTIONS = [
  {
    key: "bi/architectureDesignEngineering",
    value: "Design",
  },
  { key: "bi/construction", value: "Construction" },
  { key: "bi/concessions", value: "Concessions" },
  { key: "bi/services", value: "Services" },
  { key: "bi/other", value: "Others" }
];

const MARKET_OPTIONS = [
  { key: "mi/chicago", value: "Chicago (ORD)" },
  { key: "mi/losAngles", value: "Los Angeles (LAX)" },
  { key: "mi/newYork", value: "New York (JFK T8 & New Terminal One)" },
];

const BUSINESS_INTEREST_OPTIONS = [
  {
    key: "bi/architectureDesignEngineering",
    value: "Design",
  },
  { key: "bi/construction", value: "Construction" },
  { key: "bi/concessions", value: "Concessions" },
  { key: "bi/services", value: "Services" },
  { key: "bi/other", value: "Others" },
];

const EMPLOYMENT_INTEREST_OPTIONS = [
  { key: "ei/foodAndBeverage", value: "Food & Beverage" },
  { key: "ei/retail", value: "Retail" },
  { key: "ei/construction", value: "Construction" },
  { key: "ei/maintenance", value: "Maintenance" },
  { key: "ei/professionalServices", value: "Professional Services" },
];

const CERTIFICATIONS_OPTIONS = [
  { key: "cert/acdbe", value: "ACDBE - Airport Concessions Disadvantaged Business Enterprise" },
  { key: "cert/lbe", value: "LBE - Local Business Enterprise" },
  { key: "cert/mbe", value: "MBE - Minority Business Enterprise" },
  { key: "cert/sdvosb", value: "SDVOSB - Service-Disabled Veteran-Owned Small Businesses" },
  { key: "cert/wbe", value: "WBE - Women Business Enterprise" },
  { key: "cert/sbe", value: "SBE - Small Business Enterprise" },
  { key: "cert/notCertified", value: "Not Certified" },
  { key: "cert/other", value: "Other" },
];

const COMPANY_DESCRIPTION_OPTIONS = [
  { key: "cd/womanOwned", value: "Woman-owned" },
  { key: "cd/minorityOwned", value: "Minority-owned" },
  { key: "cd/veteranOwned", value: "Veteran-owned" },
  { key: "cd/acdbe", value: "ACDBE" },
  { key: "cd/other", value: "Other" },
];

const URWAIRPORT_EMAIL_TEMPLATES = {
  REGISTER_NOW: "URW_AIRPORT_REGISTER_NOW",
};

const CONTACT_US_FILE = {
  fileName: "urwairports-contact-us",
  fields: [
    { id: "createdAt", title: "Date" }, // Don't change the order of this field
    { id: "propertyId", title: "Airport" },
    { id: "firstName", title: "Firstname" },
    { id: "lastName", title: "Lastname" },
    { id: "email", title: "Email" },
    { id: "mobile", title: "Mobile" },
    { id: "company", title: "Company" },
    { id: "message", title: "Message" },
    { id: "emailUpdates", title: "Email Updates" },


  ],
  form: FORM_TYPES.CONTACT_US,
  email: process.env.MARKETING_EMAIL,
  subject: "URW  Airports - Contact us inquiries",
};

const BUSINESS_OPPORTUNITY_FILE = {
  fileName: "nto-business-inquiries",
  fields: [
    { id: "createdAt", title: "Date" }, // Don't change the order of this field
    { id: "responseId", title: "ResponseId" },
    { id: "propertyId", title: "Airport" },
    { id: "firstName", title: "Firstname" },
    { id: "lastName", title: "Lastname" },
    { id: "email", title: "Email" },
    { id: "mobile", title: "Mobile" },
    { id: "companyName", title: "Company Name" },
    { id: "businessAddress1", title: "Business Address 1" },
    { id: "businessAddress2", title: "Business Address 2" },
    { id: "city", title: "City" },
    { id: "state", title: "State" },
    { id: "zipCode", title: "ZipCode" },
    { id: "website", title: "Website" },
    {
      id: "bi/architectureDesignEngineering",
      title: "Design",
    },
    { id: "bi/construction", title: "Construction" },
    { id: "bi/concessions", title: "Concessions" },
    { id: "bi/services", title: "Services" },
    { id: "bi/other", title: "Others" },
    { id: "aboutBusiness", title: "About Business" },
    { id: "yearsInBusiness", title: "Years In Business" },
    {
      id: "cert/acdbe",
      title: "ACDBE - Airport Concessions Disadvantaged Business Enterprise",
    },
    { id: "cert/lbe", title: "LBE - Local Business Enterprise" },
    { id: "cert/mbe", title: "MBE - Minority Business Enterprise" },
    {
      id: "cert/sdvosb",
      title: "SDVOSB - Service-Disabled Veteran-Owned Small Businesses",
    },
    { id: "cert/wbe", title: "WBE - Women Business Enterprise" },
    { id: "cert/sbe", title: "SBE - Small Business Enterprise" },
    { id: "cert/notCertified", title: "Not Certified" },
    { id: "cert/other", title: "Other" },
    { id: "airportExperience", title: "Airport Experience" },
    { id: "advancedNetworkNews", title: "Advanced Network News" },
    { id: "emailUpdates", title: "Email Updates" },
    { id: "textUpdates", title: "Text Updates" },
    { id: "termsAndConditions", title: "Terms And Conditions" },
    { id: "locale", title: "Locale" },

  ],
  form: FORM_TYPES.BUSINESS_OPPORTUNITY,
  email: process.env.MARKETING_EMAIL,
  subject: "URW Airports - NTO - Business Opportunities",
};

const EMPLOYMENT_OPPORTUNITY_FILE = {
  fileName: "nto-employment-inquiries",
  fields: [
    { id: "createdAt", title: "Date" }, // Don't change the order of this field
    { id: "responseId", title: "ResponseId" },
    { id: "propertyId", title: "Airport" },
    { id: "firstName", title: "Firstname" },
    { id: "lastName", title: "Lastname" },
    { id: "email", title: "Email" },
    { id: "mobile", title: "Mobile" },
    { id: "ei/foodAndBeverage", title: "Food & Beverage" },
    { id: "ei/retail", title: "Retail" },
    { id: "ei/construction", title: "Construction" },
    { id: "ei/maintenance", title: "Maintenance" },
    { id: "ei/professionalServices", title: "Professional Services" },
    {
      id: "airportEmploymentExperience",
      title: "Airport Employment Experience",
    },
    { id: "homeAddress1", title: "Home Address 1" },
    { id: "homeAddress2", title: "Home Address 2" },
    { id: "city", title: "City" },
    { id: "state", title: "State" },
    { id: "zipCode", title: "ZipCode" },
    { id: "advancedNetworkNews", title: "Advanced Network News" },
    { id: "emailUpdates", title: "Email Updates" },
    { id: "textUpdates", title: "Text Updates" },
    { id: "termsAndConditions", title: "Terms And Conditions" },
    { id: "locale", title: "Locale" },
  ],
  form: FORM_TYPES.EMPLOYMENT_OPPORTUNITY,
  email: process.env.MARKETING_EMAIL,
  subject: "URW Airports - NTO - Employment Opportunities",
};

const STAY_UP_TO_DATE_FILE = {
  fileName: "urwairports-stay-up-to-date",
  fields: [
    { id: "createdAt", title: "Date" }, // Don't change the order of this field
    { id: "responseId", title: "ResponseId" },
    { id: "propertyId", title: "Airport" },
    { id: "email", title: "Email" },
  ],
  form: FORM_TYPES.STAY_UP_TO_DATE,
  email: process.env.MARKETING_EMAIL,
  subject: "URW Airports - Stay in touch inquiries",
};

const REGISTER_NOW_FILE = {
  fileName: "urwairports-register-now",
  fields: [
    { id: "createdAt", title: "Date" }, // Don't change the order of this field
    { id: "responseId", title: "ResponseId" },
    { id: "propertyId", title: "Airport" },

    { id: "firstName", title: "Firstname" },
    { id: "lastName", title: "Lastname" },
    { id: "email", title: "Email" },
    { id: "mobile", title: "Mobile" },
    { id: "company", title: "Company Name" },
    { id: "website", title: "Website" },

    {
      id: "bi/architectureDesignEngineering",
      title: "Design",
    },
    { id: "bi/construction", title: "Construction" },
    { id: "bi/concessions", title: "Concessions" },
    { id: "bi/services", title: "Services" },
    { id: "bi/other", title: "Others" },
    { id: "cd/womanOwned", title: "Woman-owned" },
    { id: "cd/minorityOwned", title: "Minority-owned" },
    { id: "cd/veteranOwned", title: "Veteran-owned" },
    { id: "cd/acdbe", title: "ACDBE" },
    { id: "cd/other", title: "Other" },
    { id: "mi/chicago", title: "Chicago (ORD)" },
    { id: "mi/losAngles", title: "Los Angeles (LAX)" },
    { id: "mi/newYork", title: "New York (JFK T8 & New Terminal One)" },

    { id: "otherInformation", title: "Anything you would like us to know" },

    { id: "registerNowEmailUpdates", title: "Email Updates" },

    // { id: 'createdAt', title: 'Date' },
  ],
  //   filter: "ALL",
  //   categoryLength: 8,
  //   Note : Combined records for All Inquiry and Holt in one CSV file
  // filter: (r) =>
  // (r["bi/architectureDesignEngineering"] &&
  //   r["bi/construction"] &&
  //   r["bi/concessions"] &&
  //   r["bi/services"] &&
  //   r["bi/other"] || (r["bi/architectureDesignEngineering"] || r["bi/construction"])),
  form: FORM_TYPES.REGISTER_NOW,
  email: process.env.MARKETING_EMAIL,
  subject: "URW Airports - Register Now - Inquiry Form Responses",
};

const REGISTER_NOW_FILE_HOLT = {
  fileName: "urwairports-business-inquiries-holt",
  fields: REGISTER_NOW_FILE.fields,
  //   filter: ["Architecture/Design Engineering", "Construction"],
  filter: (r) => r["bi/architectureDesignEngineering"] || r["bi/construction"],
  form: FORM_TYPES.REGISTER_NOW,
  email: process.env.HOLT_EMAIL,
  subject: "URW Airports - Business Opportunities - Construction / Engineering",
};

const REGISTER_NOW_FILE_LEASING = {
  fileName: "urwairports-business-inquiries-leasing",
  fields: REGISTER_NOW_FILE.fields,
  //   filter: [
  //     "Food & Beverage",
  //     "Product Manufacturer/Maker",
  //     "Retail Business (Brick & Mortar)",
  //     "Retail Business (Online)",
  //   ],
  filter: (r) => r["bi/concessions"],
  form: FORM_TYPES.REGISTER_NOW,
  email: process.env.LEASING_EMAIL,
  subject: "URW Airports - Business Opportunities - Leasing Inquires",
};

const REGISTER_NOW_FILE_OPS = {
  fileName: "urwairports-business-inquiries-ops",
  fields: REGISTER_NOW_FILE.fields,
  //   filter: ["Supplier", "Other"],
  filter: (r) => r["bi/services"] || r["bi/other"],
  form: FORM_TYPES.REGISTER_NOW,
  email: process.env.OPS_EMAIL,
  subject: "URW Airports - Business Opportunities - Suppliers / Ops",
};

const FILES = [CONTACT_US_FILE, STAY_UP_TO_DATE_FILE, REGISTER_NOW_FILE];

module.exports = {
  DYNAMO_TABLES,
  FORM_TYPES,
  REGISTRATION_OPTIONS,
  BUSINESS_INTEREST_OPTIONS,
  EMPLOYMENT_INTEREST_OPTIONS,
  CERTIFICATIONS_OPTIONS,
  MARKET_OPTIONS,
  COMPANY_DESCRIPTION_OPTIONS,
  FILES,
  URWAIRPORT_EMAIL_TEMPLATES,
};
